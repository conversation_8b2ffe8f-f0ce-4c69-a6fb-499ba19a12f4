"""
DSSM-Lite: Lightweight Decomposed State Space Model for Stereo Matching
A highly efficient alternative to RAFT-GRU with explicit state decomposition
"""

import torch
import torch.nn as nn
import torch.nn.functional as F


# ============================================================================
# Helper Modules
# ============================================================================

class DepthwiseSeparableConv(nn.Module):
    """Depthwise Separable Convolution for efficient computation"""
    def __init__(self, in_channels, out_channels, kernel_size=3, stride=1, padding=1, dilation=1):
        super().__init__()
        self.depthwise = nn.Conv2d(
            in_channels, in_channels, kernel_size,
            stride=stride, padding=padding, dilation=dilation, 
            groups=in_channels, bias=False
        )
        self.pointwise = nn.Conv2d(in_channels, out_channels, 1, bias=False)
        self.bn = nn.BatchNorm2d(out_channels)
        self.relu = nn.ReLU(inplace=True)
        
    def forward(self, x):
        x = self.depthwise(x)
        x = self.pointwise(x)
        x = self.bn(x)
        x = self.relu(x)
        return x


class CBAM(nn.Module):
    """Convolutional Block Attention Module"""
    def __init__(self, channels, reduction_ratio=4):
        super().__init__()
        # Channel attention
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
        self.fc1 = nn.Conv2d(channels, channels // reduction_ratio, 1, bias=False)
        self.relu = nn.ReLU(inplace=True)
        self.fc2 = nn.Conv2d(channels // reduction_ratio, channels, 1, bias=False)
        self.sigmoid = nn.Sigmoid()
        
        # Spatial attention
        self.conv_spatial = nn.Conv2d(2, 1, 7, padding=3, bias=False)
        
    def forward(self, x):
        # Channel attention
        avg_out = self.fc2(self.relu(self.fc1(self.avg_pool(x))))
        max_out = self.fc2(self.relu(self.fc1(self.max_pool(x))))
        channel_att = self.sigmoid(avg_out + max_out)
        x = x * channel_att
        
        # Spatial attention
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        spatial_att = self.sigmoid(self.conv_spatial(torch.cat([avg_out, max_out], dim=1)))
        x = x * spatial_att
        
        return x


# ============================================================================
# State Initialization
# ============================================================================

class StateInitializer(nn.Module):
    """Initialize the three decomposed states at t=0"""
    def __init__(self, geo_dim=32, conf_dim=16, ctx_dim=32):
        super().__init__()
        # Geometry state initializer
        self.geo_init = nn.Sequential(
            nn.Conv2d(32, 64, 3, padding=1),  # from context features
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, geo_dim, 1)
        )
        
        # Confidence state initializer
        self.conf_init = nn.Sequential(
            nn.Conv2d(18, 32, 3, padding=1),  # from cost volume
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True),
            nn.Conv2d(32, conf_dim, 1),
            nn.Tanh()
        )
        
        # Context state initializer
        self.ctx_init = nn.Sequential(
            nn.Conv2d(49, 64, 3, padding=1),  # from neighborhood features (updated from 36 to 49)
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, ctx_dim, 1)
        )
        
    def forward(self, f_geo, f_context, f_neig, disp):
        """
        Args:
            f_geo: (B, 18, H, W) - neighborhood cost volume
            f_context: (B, 32, H, W) - left view context features
            f_neig: (B, 36, H, W) - neighborhood similarity features
            disp: (B, 1, H, W) - current disparity
        Returns:
            h_geometry: (B, 32, H, W)
            h_confidence: (B, 16, H, W)
            h_context: (B, 32, H, W)
        """
        h_geometry = self.geo_init(f_context)
        h_confidence = self.conf_init(f_geo)
        h_context = self.ctx_init(f_neig)
        
        # Modulate geometry with disparity
        h_geometry = h_geometry * torch.exp(-0.1 * disp.abs())
        
        return h_geometry, h_confidence, h_context


# ============================================================================
# Efficient Feature Encoder
# ============================================================================

class EfficientFeatureEncoder(nn.Module):
    """Single-path feature encoding with shared representations"""
    def __init__(self):
        super().__init__()
        # Feature compression
        self.cost_compress = nn.Conv2d(18, 64, 1)
        self.context_compress = nn.Conv2d(32, 64, 1)
        self.neig_compress = nn.Conv2d(49, 64, 1)
        self.disp_embed = nn.Conv2d(1, 64, 7, padding=3)
        
        # Shared fusion
        self.shared_fusion = nn.Sequential(
            nn.Conv2d(256, 256, 1),  # Total: 64 channels
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 128, 3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True)
        )
        
        # Modulation generators
        self.geo_modulator = nn.Conv2d(128, 8, 1)
        self.conf_modulator = nn.Conv2d(128, 4, 1)
        self.ctx_modulator = nn.Conv2d(128, 8, 1)
        
    def forward(self, f_geo, f_context, f_neig, disp):
        """
        Args:
            f_geo: (B, 18, H, W) - neighborhood cost volume
            f_context: (B, 32, H, W) - left view context features
            f_neig: (B, 36, H, W) - neighborhood similarity features
            disp: (B, 1, H, W) - current disparity
        Returns:
            shared_features: (B, 64, H, W)
            modulators: tuple of (geo_mod, conf_mod, ctx_mod)
        """
        # Compress features
        f_cost = self.cost_compress(f_geo)
        f_ctx = self.context_compress(f_context)
        f_n = self.neig_compress(f_neig)
        f_d = self.disp_embed(disp)
        
        # Fuse all features
        fused = self.shared_fusion(torch.cat([f_cost, f_ctx, f_n, f_d], dim=1))

        # 自注意力

        # Generate modulation signals
        geo_mod = self.geo_modulator(fused)
        conf_mod = self.conf_modulator(fused)
        ctx_mod = self.ctx_modulator(fused)

        return fused, (geo_mod, conf_mod, ctx_mod)


# ============================================================================
# State Updaters
# ============================================================================

class LiteGeometryUpdater(nn.Module):
    """Efficient geometry state updater using depthwise separable convolutions"""
    def __init__(self, state_dim=32, shared_dim=128):
        super().__init__()
        # Multi-scale depthwise separable blocks
        self.conv1 = DepthwiseSeparableConv(state_dim + shared_dim + 8, state_dim, 3, padding=1, dilation=1)
        self.conv2 = DepthwiseSeparableConv(state_dim, state_dim, 3, padding=2, dilation=2)
        self.conv3 = DepthwiseSeparableConv(state_dim, state_dim, 3, padding=4, dilation=4)
        
        # Gating mechanism
        self.gate = nn.Sequential(
            nn.Conv2d(state_dim * 2, state_dim, 1),
            nn.Sigmoid()
        )
        
    def forward(self, h_geometry, shared_features, geo_modulator):
        """Update geometry state"""
        x = torch.cat([h_geometry, shared_features, geo_modulator], dim=1)

        # Multi-scale feature extraction with residual connections
        feat = self.conv1(x)
        feat = feat + self.conv2(feat)
        feat = feat + self.conv3(feat)
        
        # Gated update
        gate = self.gate(torch.cat([h_geometry, feat], dim=1))
        h_geometry_new = gate * feat + (1 - gate) * h_geometry
        
        return h_geometry_new


class LiteConfidenceUpdater(nn.Module):
    """Lightweight confidence state updater"""
    def __init__(self, state_dim=16, shared_dim=128):
        super().__init__()
        # Confidence update network
        self.confidence_net = nn.Sequential(
            nn.Conv2d(state_dim + shared_dim + 4, 32, 3, padding=1),
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True),
            nn.Conv2d(32, 32, 3, padding=1),
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True),
            nn.Conv2d(32, state_dim, 1)
        )
        
        # Simple occlusion detector
        self.occlusion_detector = nn.Sequential(
            nn.Conv2d(shared_dim, 16, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(16, 1, 1),
            nn.Sigmoid()
        )
        
    def forward(self, h_confidence, shared_features, conf_modulator):
        """Update confidence state"""
        # Detect occlusion
        occlusion_prob = self.occlusion_detector(shared_features)
        
        # Update confidence
        x = torch.cat([h_confidence, shared_features, conf_modulator], dim=1)
        conf_update = self.confidence_net(x)
        
        # Apply occlusion-aware update
        h_confidence_new = h_confidence + conf_update * (1 - 0.5 * occlusion_prob)
        h_confidence_new = torch.tanh(h_confidence_new)
        
        return h_confidence_new, occlusion_prob


class LiteContextUpdater(nn.Module):
    """Context state updater with CBAM attention"""
    def __init__(self, state_dim=32, shared_dim=128):
        super().__init__()
        # Main update path
        self.context_conv = nn.Sequential(
            nn.Conv2d(state_dim + shared_dim + 8, 64, 3, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, state_dim, 3, padding=1)
        )
        
        # CBAM attention
        self.cbam = CBAM(state_dim, reduction_ratio=4)
        
        # Residual weight
        self.residual_weight = nn.Parameter(torch.tensor(0.1))
        
    def forward(self, h_context, shared_features, ctx_modulator):
        """Update context state"""
        x = torch.cat([h_context, shared_features, ctx_modulator], dim=1)
        update = self.context_conv(x)
        
        # Apply CBAM attention
        update_attended = self.cbam(update)
        
        # Residual connection
        h_context_new = h_context + self.residual_weight * update_attended
        
        return h_context_new


# ============================================================================
# State Interaction Module
# ============================================================================

class LiteStateInteraction(nn.Module):
    """Ultra-lightweight state interaction via 1x1 convolutions"""
    def __init__(self, geo_dim=32, conf_dim=16, ctx_dim=32):
        super().__init__()
        total_dim = geo_dim + conf_dim + ctx_dim
        
        # Interaction network
        self.interaction_net = nn.Sequential(
            nn.Conv2d(total_dim, total_dim, 1),
            nn.BatchNorm2d(total_dim),
            nn.ReLU(inplace=True),
            nn.Conv2d(total_dim, total_dim * 2, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(total_dim * 2, total_dim, 1)
        )
        
        # Decomposition projections
        self.to_geo = nn.Conv2d(total_dim, geo_dim, 1)
        self.to_conf = nn.Conv2d(total_dim, conf_dim, 1)
        self.to_ctx = nn.Conv2d(total_dim, ctx_dim, 1)
        
        self.residual_weight = 0.3
        
    def forward(self, h_geometry, h_confidence, h_context, occlusion_prob=None):
        """Cross-state interaction"""
        # Concatenate all states
        h_concat = torch.cat([h_geometry, h_confidence, h_context], dim=1)
        
        # Single interaction pass
        h_interacted = self.interaction_net(h_concat)
        
        # Decompose back to three states
        h_geo_delta = self.to_geo(h_interacted)
        h_conf_delta = self.to_conf(h_interacted)
        h_ctx_delta = self.to_ctx(h_interacted)
        
        # Residual connections
        h_geo_final = h_geometry + self.residual_weight * h_geo_delta
        h_conf_final = h_confidence + self.residual_weight * h_conf_delta
        h_ctx_final = h_context + self.residual_weight * h_ctx_delta
        
        # Optional occlusion-based adjustment
        if occlusion_prob is not None:
            h_geo_final = h_geo_final * (1 - 0.3 * occlusion_prob)
            h_ctx_final = h_ctx_final * (1 + 0.2 * occlusion_prob)
        
        return h_geo_final, h_conf_final, h_ctx_final


# ============================================================================
# Prediction Head
# ============================================================================

class LitePredictionHead(nn.Module):
    """Simple but effective prediction head"""
    def __init__(self, geo_dim=32, conf_dim=16, ctx_dim=32, out_dim=1):
        super().__init__()
        total_dim = geo_dim + conf_dim + ctx_dim
        
        self.predictor = nn.Sequential(
            nn.Conv2d(total_dim, 64, 1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, 32, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(32, out_dim, 1)  # Output disparity residual
        )
        
    def forward(self, h_geometry, h_confidence, h_context):
        """Predict disparity residual"""
        h_all = torch.cat([h_geometry, h_confidence, h_context], dim=1)
        disparity_residual = self.predictor(h_all)
        return disparity_residual


# ============================================================================
# Main DSSM-Lite Module
# ============================================================================

class DSSM_update(nn.Module):
    """
    Decomposed State Space Model for Stereo Matching
    
    Args:
        geo_dim: Dimension of geometry state (default: 32)
        conf_dim: Dimension of confidence state (default: 16)
        ctx_dim: Dimension of context state (default: 32)
        
    Input dimensions:
        f_geo: (B, 18, H, W) - neighborhood cost volume
        f_context: (B, 32, H, W) - left view context features
        f_neig: (B, 36, H, W) - neighborhood similarity features
        disp: (B, 1, H, W) - current disparity
        
    Returns:
        disparity_residual: (B, 1, H, W) - predicted disparity update
        hidden_states: tuple of three updated states
    """
    
    def __init__(self, geo_dim=32, conf_dim=16, ctx_dim=32):
        super().__init__()
        
        # State dimensions
        self.geo_dim = geo_dim
        self.conf_dim = conf_dim
        self.ctx_dim = ctx_dim
        
        # Initialize modules
        self.state_initializer = StateInitializer(geo_dim, conf_dim, ctx_dim)
        self.feature_encoder = EfficientFeatureEncoder()
        
        # State updaters
        self.geo_updater = LiteGeometryUpdater(geo_dim, 128)
        self.conf_updater = LiteConfidenceUpdater(conf_dim, 128)
        self.ctx_updater = LiteContextUpdater(ctx_dim, 128)
        
        # Interaction and prediction
        self.state_interaction = LiteStateInteraction(geo_dim, conf_dim, ctx_dim)
        self.prediction_head = LitePredictionHead(geo_dim, conf_dim, ctx_dim)
        self.mask_head = LitePredictionHead(geo_dim, conf_dim, ctx_dim, out_dim=32)
        
    def forward(self, f_geo, f_context, f_neig, disp, hidden_states):
        """
        Forward pass of DSSM-Lite

        Args:
            f_geo: (B, 18, H, W) - neighborhood cost volume
            f_context: (B, 32, H, W) - left view context features
            f_neig: (B, 49, H, W) - neighborhood similarity features
            disp: (B, 1, H, W) - current disparity
            hidden_states: Optional tuple of (h_geo, h_conf, h_ctx) from previous iteration
            num_iterations: Number of refinement iterations (for single forward pass, set to 1)

        Returns:
            disparity_residual: (B, 1, H, W) - predicted disparity update
            new_hidden_states: Tuple of updated states (h_geo, h_conf, h_ctx)
        """

        # Initialize or inherit states
        # 检查是否是正确的DSSM隐藏状态格式
        is_valid_dssm_states = (
            isinstance(hidden_states, (list, tuple)) and
            len(hidden_states) == 3 and
            all(hasattr(state, 'shape') for state in hidden_states) and
            hidden_states[0].shape[1] == self.geo_dim and  # geo_dim = 32
            hidden_states[1].shape[1] == self.conf_dim and  # conf_dim = 16
            hidden_states[2].shape[1] == self.ctx_dim  # ctx_dim = 32
        )

        if not is_valid_dssm_states:
            # Initialize states using StateInitializer
            h_geo, h_conf, h_ctx = self.state_initializer(f_geo, f_context, f_neig, disp)
        else:
            h_geo, h_conf, h_ctx = hidden_states

        # Single iteration update (for multi-iteration, wrap this in a loop)
        # Encode features
        shared_features, (geo_mod, conf_mod, ctx_mod) = self.feature_encoder(
            f_geo, f_context, f_neig, disp
        )
        
        # Update states in parallel
        h_geo_new = self.geo_updater(h_geo, shared_features, geo_mod)
        h_conf_new, occlusion = self.conf_updater(h_conf, shared_features, conf_mod)
        h_ctx_new = self.ctx_updater(h_ctx, shared_features, ctx_mod)
        
        # State interaction
        h_geo_final, h_conf_final, h_ctx_final = self.state_interaction(
            h_geo_new, h_conf_new, h_ctx_new, occlusion
        )
        
        # Predict disparity residual
        disparity_residual = self.prediction_head(h_geo_final, h_conf_final, h_ctx_final)
        mask_feat = F.relu(self.mask_head(h_geo_final, h_conf_final, h_ctx_final))
        
        # Return residual and updated states
        new_hidden_states = (h_geo_final, h_conf_final, h_ctx_final)
        
        return disparity_residual, mask_feat, new_hidden_states


# ============================================================================
# Usage Example
# ============================================================================

def test_dssm_lite():
    """Test the DSSM-Lite module"""
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Create model
    model = DSSM_update(geo_dim=32, conf_dim=16, ctx_dim=32).to(device)
    
    # Create dummy inputs
    batch_size = 2
    height, width = 256, 256
    
    f_geo = torch.randn(batch_size, 18, height, width).to(device)
    f_context = torch.randn(batch_size, 32, height, width).to(device)
    f_neig = torch.randn(batch_size, 36, height, width).to(device)
    disp = torch.randn(batch_size, 1, height, width).to(device)
    
    # Initialize hidden states with correct dimensions
    h_geo = torch.randn(batch_size, 32, height, width).to(device)      # geometry state: 32 channels
    h_conf = torch.randn(batch_size, 16, height, width).to(device)     # confidence state: 16 channels
    h_ctx = torch.randn(batch_size, 32, height, width).to(device)      # context state: 32 channels
    hidden_states = (h_geo, h_conf, h_ctx)
    
    # Forward pass
    with torch.no_grad():
        disparity_residual, hidden_states = model(f_geo, f_context, f_neig, disp, hidden_states)
    
    print(f"Input shapes:")
    print(f"  f_geo: {f_geo.shape}")
    print(f"  f_context: {f_context.shape}")
    print(f"  f_neig: {f_neig.shape}")
    print(f"  disp: {disp.shape}")
    print(f"\nOutput shapes:")
    print(f"  disparity_residual: {disparity_residual.shape}")
    print(f"  h_geometry: {hidden_states[0].shape}")
    print(f"  h_confidence: {hidden_states[1].shape}")
    print(f"  h_context: {hidden_states[2].shape}")
    
    # Test iterative refinement
    print("\nTesting iterative refinement...")
    current_disp = disp.clone()
    for i in range(3):
        residual, hidden_states = model(f_geo, f_context, f_neig, current_disp, hidden_states)
        current_disp = current_disp + residual
        print(f"  Iteration {i+1}: residual norm = {residual.norm().item():.4f}")
    
    print("\nModel parameter count:")
    total_params = sum(p.numel() for p in model.parameters())
    print(f"  Total parameters: {total_params:,}")
    
    # Compute FLOPs estimate
    from torch.profiler import profile, ProfilerActivity
    
    with profile(activities=[ProfilerActivity.CPU, ProfilerActivity.CUDA],
                 record_shapes=True) as prof:
        with torch.no_grad():
            _ = model(f_geo, f_context, f_neig, disp, hidden_states)
    
    print("\nDSSM-Lite module successfully tested!")
    return model


if __name__ == "__main__":
    # Run test
    model = test_dssm_lite()
    
    print("\n" + "="*60)
    print("DSSM-Lite is ready for integration!")
    print("="*60)
    
    