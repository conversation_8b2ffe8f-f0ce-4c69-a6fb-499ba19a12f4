import os
os.environ["CUDA_VISIBLE_DEVICES"] = "2"
import torch
import time
import sys


# 添加路径
sys.path.append('./core')
sys.path.append('./Depth-Anything-V2-list3')

print("🚀 开始简单性能测试")
print(f"PyTorch版本: {torch.__version__}")
print(f"CUDA可用: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"GPU: {torch.cuda.get_device_name()}")
    print(f"显存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")


def test_monster_plus():
    """测试Monster Plus"""
    print("\n🔥 测试Monster Plus...")
    try:
        # from monste_plus import Monster
        from vggt_stereo import VGGT_Ste as Monster
        
        # 创建参数
        class Args:
            def __init__(self):
                self.encoder = 'vitl'
                self.max_disp = 768
                self.mixed_precision = False
                self.n_gru_layers = 3
                self.hidden_dims = [128, 128, 128]
                self.corr_levels = 2
                self.corr_radius = 4
                self.n_downsample = 2  # 🔥 添加缺失的参数
                self.level_switch_iter = 8
                self.enable_level_optimization = True
                self.neighborhood_refine_iters = 4
                self.neighborhood_size = 7
                self.enable_cross_attention = True
                self.enable_self_attention = True
                self.confidence_threshold = 0.5
                self.detail_enhancement = True
        
        args = Args()
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 固定随机种子确保数据一致
        torch.manual_seed(42)
        image1 = torch.randn(1, 3, 384, 768).to(device) 
        image2 = torch.randn(1, 3, 384, 768).to(device) 
        
        print("   📷 测试数据准备完成 (224×448)")
        
        # 测试模型加载
        start_time = time.time()
        model = Monster(args).to(device)
        model.eval()
        load_time = time.time() - start_time
        
        # 统计参数
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"   ⏱️  模型加载时间: {load_time:.4f}s")
        print(f"   🔢 总参数量: {total_params:,}")
        print(f"   🎯 可训练参数: {trainable_params:,}")
        
        # 🔥 正确的性能测试
        with torch.no_grad():
            # 预热5次
            print("   🔥 预热中...")
            for _ in range(5):
                _ = model(image1, image2, iters=32, test_mode=True)
            
            # 确保GPU操作完成并重置显存统计
            if torch.cuda.is_available():
                torch.cuda.synchronize()
                torch.cuda.reset_peak_memory_stats()
            
            # 🔥 运行100次测试
            print("   📊 运行100次测试中...")
            inference_times = []
            num_runs = 100
            max_memory = 0
            
            for i in range(num_runs):
                # 重置当前轮次的显存统计
                if torch.cuda.is_available():
                    torch.cuda.reset_peak_memory_stats()
                    torch.cuda.synchronize()
                
                # 开始计时
                start_time = time.time()
                result = model(image1, image2, iters=32, test_mode=True)
                if isinstance(result, tuple) and len(result) >= 2:
                    output, init_disp = result[0], result[1]
                else:
                    output = result
                
                # 确保GPU计算完成
                if torch.cuda.is_available():
                    torch.cuda.synchronize()
                
                # 记录时间
                end_time = time.time()
                inference_times.append(end_time - start_time)
                
                # 记录本轮最大显存
                if torch.cuda.is_available():
                    current_memory = torch.cuda.max_memory_allocated() / 1024 / 1024
                    max_memory = max(max_memory, current_memory)
                
                # 每20次显示进度
                if (i + 1) % 20 == 0:
                    print(f"      进度: {i+1}/{num_runs}, 当前平均时间: {sum(inference_times[-20:])/20:.4f}s")
        
        # 计算统计数据
        avg_time = sum(inference_times) / len(inference_times)
        min_time = min(inference_times)
        max_time = max(inference_times)
        std_dev = (sum([(t - avg_time)**2 for t in inference_times]) / len(inference_times))**0.5
        
        print(f"   ⏱️  推理时间统计 ({num_runs}次运行):")
        print(f"      平均时间: {avg_time:.4f}s")
        print(f"      最小时间: {min_time:.4f}s")
        print(f"      最大时间: {max_time:.4f}s")
        print(f"      标准差:   {std_dev:.4f}s")
        print(f"   📊 输出尺寸: {output.shape}")
        print(f"   💾 100次测试中的峰值显存: {max_memory:.1f}MB")
            
        return {
            'model': 'Monster Plus',
            'load_time': load_time,
            'inference_time': avg_time,
            'inference_time_min': min_time,
            'inference_time_max': max_time,
            'inference_time_std': std_dev,
            'total_params': total_params,
            'trainable_params': trainable_params,
            'peak_memory': max_memory,
            'num_runs': num_runs
        }
        
    except Exception as e:
        print(f"❌ Monster Plus测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_monster_original():
    """测试Original Monster"""
    print("\n🔥 测试Original Monster...")
    try:
        from monster_orgin import Monster
        
        # 创建参数 - 添加缺失的参数
        class Args:
            def __init__(self):
                self.encoder = 'vitl'
                self.max_disp = 768
                self.mixed_precision = False
                self.n_gru_layers = 3
                self.hidden_dims = [128, 128, 128]
                self.corr_levels = 2
                self.corr_radius = 4
                self.n_downsample = 2  # 添加缺失的参数
        
        args = Args()
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 使用相同的随机种子
        torch.manual_seed(42)
        image1 = torch.randn(1, 3, 384, 768).to(device) 
        image2 = torch.randn(1, 3, 384, 768).to(device)
                
        # 测试模型加载
        start_time = time.time()
        model = Monster(args).to(device)
        model.eval()
        load_time = time.time() - start_time
        
        # 统计参数
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"   ⏱️  模型加载时间: {load_time:.4f}s")
        print(f"   🔢 总参数量: {total_params:,}")
        print(f"   🎯 可训练参数: {trainable_params:,}")
        
        # 🔥 正确的性能测试
        with torch.no_grad():
            # 预热5次
            print("   🔥 预热中...")
            for _ in range(5):
                _ = model(image1, image2, iters=32, test_mode=True)
            
            # 确保GPU操作完成并重置显存统计
            if torch.cuda.is_available():
                torch.cuda.synchronize()
                torch.cuda.reset_peak_memory_stats()
            
            # 🔥 运行100次测试
            print("   📊 运行100次测试中...")
            inference_times = []
            num_runs = 100
            max_memory = 0
            
            for i in range(num_runs):
                # 重置当前轮次的显存统计
                if torch.cuda.is_available():
                    torch.cuda.reset_peak_memory_stats()
                    torch.cuda.synchronize()
                
                # 开始计时
                start_time = time.time()
                result = model(image1, image2, iters=32, test_mode=True)
                if isinstance(result, tuple):
                    output = result[0]
                else:
                    output = result
                
                # 确保GPU计算完成
                if torch.cuda.is_available():
                    torch.cuda.synchronize()
                
                # 记录时间
                end_time = time.time()
                inference_times.append(end_time - start_time)
                
                # 记录本轮最大显存
                if torch.cuda.is_available():
                    current_memory = torch.cuda.max_memory_allocated() / 1024 / 1024
                    max_memory = max(max_memory, current_memory)
                
                # 每20次显示进度
                if (i + 1) % 20 == 0:
                    print(f"      进度: {i+1}/{num_runs}, 当前平均时间: {sum(inference_times[-20:])/20:.4f}s")
        
        # 计算统计数据
        avg_time = sum(inference_times) / len(inference_times)
        min_time = min(inference_times)
        max_time = max(inference_times)
        std_dev = (sum([(t - avg_time)**2 for t in inference_times]) / len(inference_times))**0.5
        
        print(f"   ⏱️  推理时间统计 ({num_runs}次运行):")
        print(f"      平均时间: {avg_time:.4f}s")
        print(f"      最小时间: {min_time:.4f}s")
        print(f"      最大时间: {max_time:.4f}s")
        print(f"      标准差:   {std_dev:.4f}s")
        print(f"   📊 输出尺寸: {output.shape}")
        print(f"   💾 100次测试中的峰值显存: {max_memory:.1f}MB")
            
        return {
            'model': 'Original Monster',
            'load_time': load_time,
            'inference_time': avg_time,
            'inference_time_min': min_time,
            'inference_time_max': max_time,
            'inference_time_std': std_dev,
            'total_params': total_params,
            'trainable_params': trainable_params,
            'peak_memory': max_memory,
            'num_runs': num_runs
        }
        
    except Exception as e:
        print(f"❌ Original Monster测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def compare_results(plus_result, orig_result):
    """比较结果"""
    if not plus_result or not orig_result:
        print("❌ 无法比较，某个模型测试失败")
        return
        
    print(f"\n{'='*60}")
    print(f"🔥 性能对比结果 (基于{plus_result['num_runs']}次运行)")
    print(f"{'='*60}")
    
    print(f"\n📊 参数量对比:")
    print(f"   Monster Plus: {plus_result['total_params']:,}")
    print(f"   Original Monster: {orig_result['total_params']:,}")
    param_diff = plus_result['total_params'] - orig_result['total_params']
    param_ratio = plus_result['total_params'] / orig_result['total_params']
    print(f"   差异: {param_diff:+,} ({param_ratio:.2f}x)")
    
    print(f"\n⏱️  推理时间对比:")
    print(f"   Monster Plus 平均时间: {plus_result['inference_time']:.4f}s ± {plus_result['inference_time_std']:.4f}s")
    print(f"   Original Monster 平均时间: {orig_result['inference_time']:.4f}s ± {orig_result['inference_time_std']:.4f}s")
    speedup = orig_result['inference_time'] / plus_result['inference_time']
    print(f"   速度比: {speedup:.2f}x")
    
    if speedup > 1:
        print(f"   🚀 Monster Plus 更快!")
    else:
        print(f"   🐌 Monster Plus 更慢!")
    
    print(f"\n💾 显存对比:")
    print(f"   Monster Plus 峰值显存: {plus_result['peak_memory']:.1f}MB")
    print(f"   Original Monster 峰值显存: {orig_result['peak_memory']:.1f}MB")
    memory_diff = plus_result['peak_memory'] - orig_result['peak_memory']
    if memory_diff > 0:
        print(f"   📈 Monster Plus 多用了 {memory_diff:.1f}MB")
    else:
        print(f"   📉 Monster Plus 少用了 {abs(memory_diff):.1f}MB")
    
    print(f"\n🎯 效率指标:")
    plus_fps = 1.0 / plus_result['inference_time']
    orig_fps = 1.0 / orig_result['inference_time']
    print(f"   Monster Plus FPS: {plus_fps:.2f}")
    print(f"   Original Monster FPS: {orig_fps:.2f}")
    
    # 参数效率 (FPS per million parameters)
    plus_efficiency = plus_fps / (plus_result['total_params'] / 1e6)
    orig_efficiency = orig_fps / (orig_result['total_params'] / 1e6)
    print(f"   Monster Plus 参数效率: {plus_efficiency:.3f} FPS/M参数")
    print(f"   Original Monster 参数效率: {orig_efficiency:.3f} FPS/M参数")

if __name__ == "__main__":
    # 测试两个模型
    plus_result = test_monster_plus()
    
    # 清理显存
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        import gc
        gc.collect()
        
    # orig_result = test_monster_original()
    
    # 比较结果
    # compare_results(plus_result, orig_result)
    
    print(f"\n✅ 测试完成!") 